@charset "utf-8";

/* CSS Document */
body,
html {
	min-width: 1400px;
	overflow-x: hidden;
}

body {
	margin: 0;
	padding: 0;
	font-family: "Microsoft YaHei", "微软雅黑", Arial, Helvetica, SimHei, sans-serif;
	font-size: 12px;
	color: #000;
	background: linear-gradient(to right, #960516, #22164b);
	overflow: hidden;
}

ul,
li,
p,
h1,
h2,
h3,
h4,
h5,
h6,
span,
dl,
dt,
dd,
img,
table,
tr,
td,
input,
a,
strong,
ol,
li,
select,
option,
button {
	margin: 0;
	padding: 0;
	list-style: none;
	border: 0;
	outline: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: normal;
}

a {
	outline: 0;
	color: #525252;
	text-decoration: none;
}

i,
em {
	font-style: normal;
}

img {
	border: 0 none;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.clearfix:after {
	display: block;
	content: "clear";
	height: 0;
	clear: both;
	overflow: hidden;
	visibility: hidden;
}

input {
	outline: none;
	background: none;
}

input[type="button"],
input[type="submit"],
input[type="reset"] {
	-webkit-appearance: none;
}

input::-webkit-input-placeholder {
	/* WebKit browsers */
	color: #e22828;
}

input:-moz-placeholder {
	/* Mozilla Firefox 4 to 18 */
	color: #e22828;
}

input::-moz-placeholder {
	/* Mozilla Firefox 19+ */
	color: #e22828;
}

input:-ms-input-placeholder {
	/* Internet Explorer 10+ */
	color: #e22828;
}

.tar {
	text-align: right;
}

.tal {
	text-align: left;
}

.tac {
	text-align: center;
}

.row {
	font-size: 0;
	/* 所有浏览器 */
}

.row .col {
	letter-spacing: normal;
	word-spacing: normal;
	vertical-align: top;
}

.col {
	display: inline-block;
	*display: inline;
	*zoom: 1;
}