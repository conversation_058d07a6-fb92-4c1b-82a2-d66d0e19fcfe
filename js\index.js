$(function () {

	$('body').find("a").attr('href', function (i, url) {
		if (url !== undefined && url.indexOf('i.offcn.com') != -1) {
			url = url + getRequest();
		}
		$(this).attr('href', url);
	})

	// url跳转带参数
	function getRequest(params) {
		var str;
		var urlz = window.location.href;
		if (urlz.indexOf("?") != -1) {
			str = window.location.search;
		} else {
			str = '?owner=30822&channel=107&c2=wap'
		}
		return str;
	}

	var part1Swiper = new Swiper(".part1-swiper", {
		// 基础配置
		slidesPerView: 4,
		spaceBetween: 25,
		centeredSlides: false,
		
		// 循环配置 - 对于Swiper 10.x版本
		loop: true,
		loopAddBlankSlides: false,
		loopAdditionalSlides: 1,
		
		// 自动播放
		autoplay: {
			delay: 2000,
			disableOnInteraction: false,
			pauseOnMouseEnter: true,
		},
		
		// 导航和分页
		navigation: {
			nextEl: ".part1-swiper-button-next",
			prevEl: ".part1-swiper-button-prev",
		},
		pagination: {
			el: ".part1-swiper-pagination",
			clickable: true,
			dynamicBullets: true,
		},
		
		// 效果和速度
		effect: 'slide',
		speed: 600,
		
		// 触摸配置
		grabCursor: true,
	});

	tabSwitch('.nav-item-sub', '.section-content');

	function tabSwitch(selectors, contents) {

		var selectors = $(selectors);

		var contents = $(contents);

		selectors.each(function (i) {

			var $this = $(this);

			$this.on("mouseenter", function (e) {

				var e = e || window.event;

				e.stopPropagation ? e.stopPropagation() : e.cancelBubble = true;

				if ($this.hasClass("active")) {

					return;

				}

				$this.addClass("active").siblings().removeClass("active");

				// 先移除所有内容的激活状态
				contents.removeClass("active").hide();

				// 然后显示并激活对应的内容
				contents.eq(i).addClass("active").show().css({ opacity: 0 }).stop().animate({ opacity: 1 }, 300);

			});

		})

	}

	changeSwitch('.part2-nav', '.part2-card');

	function changeSwitch(selectors, contents) {

		var selectors = $(selectors);

		var contents = $(contents);

		selectors.each(function (i) {

			var $this = $(this);

			$this.on("mouseenter", function (e) {

				var e = e || window.event;

				e.stopPropagation ? e.stopPropagation() : e.cancelBubble = true;

				if ($this.hasClass("active")) {

					return;

				}
				// console.log(i)
				$('.part-select').removeClass("active").eq(i).addClass("active");

				$this.addClass("active").siblings().removeClass("active");

				// 先移除所有内容的激活状态
				contents.removeClass("active").hide();

				// 然后显示并激活对应的内容
				contents.eq(i).addClass("active").show().css({ opacity: 0 }).stop().animate({ opacity: 1 }, 300);

			});

		})

	}

	// 返回顶部
	$(".zg_top").click(function () {
		$("body,html").animate({
			"scrollTop": 0
		}, 800);
	});

	$(".close-btn").click(function () {
		$(".modal").fadeOut()
	})

	$(".showCourse").click(function () {
		$(".modal").fadeIn()
	})

	$(".fixed-close").click(function () {
		$(".fixed").fadeOut()
	})

	// 省份选择弹框功能
	$(".part-link").click(function (e) {
		e.preventDefault();
		$("#provinceModal").fadeIn(300);
	});

	// 关闭弹框
	$("#provinceModalClose, .province-modal-overlay").click(function () {
		$("#provinceModal").fadeOut(300);
	});

	// 省份链接点击事件 - 点击后关闭弹框
	$(".province-btn").click(function () {
		// 关闭弹框，让链接正常跳转
		$("#provinceModal").fadeOut(300);
	});

	// 按ESC键关闭弹框
	$(document).keyup(function (e) {
		if (e.keyCode === 27) { // ESC键
			$("#provinceModal").fadeOut(300);
		}
	});

		// 页面滚动

		; (function () {
			var navBtn = $(".navBtn");
			var navPos = $(".navPos");
			var navPosition = [];
			navPos.each(function () {
				navPosition.push($(this).offset().top);
			});
			var l = navPosition.length;
			var reaction = true;
			navBtn.each(function (i) {
				var $this = $(this);
				$this.on("click", function (e) {
					var e = e || window.event;
					e.stopPropagation ? e.stopPropagation() : e.cancelBubble = true;
					reaction = false;
					navBtn.removeClass("active").eq(i).addClass("active");
					$("html, body").stop().animate({ scrollTop: navPosition[i] }, 500, function () {
						reaction = true;
					});
				});
			});
			var $WINDOW = $(window);
			for (var i = 0; i < l; i++) {
				(function (index) {
					$WINDOW.on("scroll", function () {
						if (!reaction) {
							return;
						}
						if (index !== l - 1) {
							if ($WINDOW.scrollTop() >= navPosition[index] && $WINDOW.scrollTop() < navPosition[index + 1]) {
								navBtn.removeClass("active").eq(index).addClass("active");
							}
						} else {
							if ($WINDOW.scrollTop() >= navPosition[index]) {
								navBtn.removeClass("active").eq(index).addClass("active");
							}
						}
						if ($WINDOW.scrollTop() < navPosition[0]) {
							navBtn.removeClass("active");
						}
					});
				})(i);
			};
		})();

})