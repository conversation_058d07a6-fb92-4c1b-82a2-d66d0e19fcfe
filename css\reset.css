@charset "UTF-8";

/* resetCSS Document */
html {
    color: #000;
    background: #fff;
}

body {
    font: 12px/1.5 "Microsoft Yahei", Tahoma, Arial, Helvetica, sans-serif;
    word-break: break-all;
    word-wrap: break-word;
}

body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
legend,
button,
input,
textarea,
select,
th,
td {
    margin: 0;
    padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 100%;
    font-weight: normal;
}

button,
input,
select,
textarea {
    font-size: 100%;
}

button {
    cursor: pointer;
}

ul,
ol {
    list-style: none;
}

a {
    text-decoration: none;
    color: #333;
}

a:hover {
    text-decoration: none;
}

img {
    border: none;
    vertical-align: middle;
}

p {
    word-wrap: break-word;
}

em,
i {
    font-style: normal;
}

b,
th {
    font-weight: normal;
}

small {
    font-size: 12px;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

input,
select,
button {
    background: none;
    border: none;
    outline: none;
    vertical-align: middle;
    -moz-appearance: none;
    /*firefox*/
    -webkit-appearance: none;
    /*google*/
    appearance: none;
    /*IE*/
}

input[type="button"],
input[type="text"],
input[type="password"] {
    -webkit-appearance: none;
    outline: none;
}

/*-用于IOS下移除原生样式*/

select {
    -webkit-appearance: none;
    outline: none;
}

/*-用于IOS下移除原生样式*/

input[required]:invalid,
input:focus:invalid,
select[required]:invalid,
select:focus:invalid {
    box-shadow: none;
}

/*-用于firefox下required移除原生样式*/

textarea {
    resize: none;
}

/* publicCSS Document */
.clearfix:after {
    content: "";
    display: block;
    clear: both;
}

.clearfix {
    *zoom: 1;
}

.clearDiv {
    clear: both;
    height: 0 !important;
    font-size: 0;
    overflow: hidden;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.wrap {
    width: 100%;
}

.main {
    position: relative;
    margin: 0 auto;
}

.overflow {
    overflow: hidden;
}

.hide {
    display: none;
}

.block {
    display: block;
}

.inline {
    display: inline;
}

.inl-block {
    display: inline-block;
}

.wordwrap {
    word-break: break-all;
    word-wrap: break-word;
}

.nowrap {
    white-space: nowrap;
}

.br0 {
    border-right: 0 !important;
}

.mr0 {
    margin-right: 0 !important;
}

.mb0 {
    margin-bottom: 0 !important;
}

.linear-link {
    text-align: center;
    border-radius: 30px;
    background: -webkit-linear-gradient(#4472ff, #6825fb);
    /*Safari 5.1 - 6.0*/
    background: -o-linear-gradient(#4472ff, #6825fb);
    /*Opera 11.1 - 12.0*/
    background: -moz-linear-gradient(#4472ff, #6825fb);
    /*Firefox 3.6 - 15*/
    background: linear-gradient(#4472ff, #6825fb);
    /*标准的语法 默认由上到下*/
}

.linear-link:hover {
    color: #f12727;
}